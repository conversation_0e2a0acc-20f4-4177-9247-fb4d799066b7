# 错误日志可视化系统

基于Vue.js 3 + Vite + TypeScript构建的错误日志可视化应用，用于展示和分析游戏错误日志数据。

## 功能特性

- 仪表盘模块
  - 错误总数统计卡片
  - 错误类型分布饼图（使用ECharts）
  - 错误时间趋势折线图（使用ECharts）
  - 版本分布统计

- 日志搜索与过滤模块
  - 搜索框（支持错误KEY和错误信息搜索）
  - 过滤器（版本号、时间范围）
  - 错误日志列表表格（支持分页）

- 日志详情模块
  - 点击表格行可查看完整错误信息

## 技术栈

- Vue.js 3
- Vite
- TypeScript
- Element Plus
- ECharts
- Pinia
- Vue Router
- SCSS

## 安装依赖

```bash
npm install
```

## 开发环境运行

```bash
npm run dev
```

## 生产环境构建

```bash
npm run build
```

## 预览构建结果

```bash
npm run preview
```

## 项目结构

```
src/
├── assets/          # 静态资源
│   └── styles/      # 样式文件
├── components/      # 公共组件
├── router/          # 路由配置
├── stores/          # 状态管理
├── types/           # TypeScript类型定义
├── utils/           # 工具函数
├── views/           # 页面组件
│   ├── Dashboard.vue    # 仪表盘页面
│   ├── Logs.vue         # 错误日志列表页面
│   └── LogDetail.vue    # 错误日志详情页面
├── App.vue          # 根组件
└── main.ts          # 应用入口
```

## 浏览器支持

- Chrome
- Firefox
- Safari
- Edge

## 许可证

MIT