<template>
  <div class="date-range-picker">
    <el-date-picker
      v-model="dateRange"
      type="daterange"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      format="YYYY-MM-DD"
      :shortcuts="shortcuts"
      @change="handleDateChange"
      clearable
      style="width: 280px"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  modelValue?: [string, string] | null
}

interface Emits {
  (e: 'update:modelValue', value: [string, string] | null): void
  (e: 'change', value: [string, string] | null): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null
})

const emit = defineEmits<Emits>()

const dateRange = ref<[Date, Date] | null>(null)

// 快捷选项配置
const shortcuts = [
  {
    text: '今日',
    value: () => {
      const today = new Date()
      const start = new Date(today.getFullYear(), today.getMonth(), today.getDate())
      const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '昨日',
    value: () => {
      const yesterday = new Date()
      yesterday.setDate(yesterday.getDate() - 1)
      const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
      const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '上周',
    value: () => {
      const now = new Date()
      const day = now.getDay()
      const diff = now.getDate() - day - 6 // 上周一
      const start = new Date(now)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date(start)
      end.setDate(start.getDate() + 6)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '本周',
    value: () => {
      const now = new Date()
      const day = now.getDay()
      const diff = now.getDate() - day + (day === 0 ? -6 : 1) // 本周一
      const start = new Date(now)
      start.setDate(diff)
      start.setHours(0, 0, 0, 0)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '上月',
    value: () => {
      const now = new Date()
      const year = now.getMonth() === 0 ? now.getFullYear() - 1 : now.getFullYear()
      const month = now.getMonth() === 0 ? 11 : now.getMonth() - 1
      const start = new Date(year, month, 1)
      const end = new Date(year, month + 1, 0, 23, 59, 59)
      return [start, end]
    }
  },
  {
    text: '本月',
    value: () => {
      const now = new Date()
      const start = new Date(now.getFullYear(), now.getMonth(), 1)
      const end = new Date()
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '过去7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 6)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '过去30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setDate(start.getDate() - 29)
      start.setHours(0, 0, 0, 0)
      end.setHours(23, 59, 59, 999)
      return [start, end]
    }
  },
  {
    text: '最近7天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: '最近30天',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]

// 处理日期变化
const handleDateChange = (value: [Date, Date] | null) => {
  if (value && value.length === 2) {
    const dateStringRange: [string, string] = [
      formatDate(value[0]),
      formatDate(value[1])
    ]
    emit('update:modelValue', dateStringRange)
    emit('change', dateStringRange)
  } else {
    emit('update:modelValue', null)
    emit('change', null)
  }
}

// 格式化日期为字符串
const formatDate = (date: Date): string => {
  if (!(date instanceof Date) || isNaN(date.getTime())) {
    console.error('Invalid date:', date)
    return ''
  }
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 解析字符串日期为 Date 对象
const parseDate = (dateStr: string): Date => {
  return new Date(dateStr)
}

// 监听外部传入的值变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue && Array.isArray(newValue) && newValue.length === 2) {
      dateRange.value = [parseDate(newValue[0]), parseDate(newValue[1])]
    } else {
      dateRange.value = null
    }
  },
  { immediate: true }
)
</script>

<style scoped>
.date-range-picker {
  display: inline-block;
}
</style>