<template>
  <div class="log-detail-container">
    <div class="card">
      <div class="detail-header">
        <div class="detail-title">错误日志详情</div>
        <div class="detail-actions">
          <el-button @click="goBack">
            <el-icon><Back /></el-icon>
            返回
          </el-button>
        </div>
      </div>
      
      <div v-if="errorLog" class="detail-content">
        <div class="detail-item">
          <span class="detail-label">ID:</span>
          <span class="detail-value">{{ errorLog.id }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">玩家UID:</span>
          <span class="detail-value">{{ errorLog.playerUid }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">版本号:</span>
          <span class="detail-value">{{ errorLog.version }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">UI路径:</span>
          <span class="detail-value">{{ errorLog.ui }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">错误KEY:</span>
          <span class="detail-value">{{ errorLog.errorKey }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">IP地址:</span>
          <span class="detail-value">{{ errorLog.ip }}</span>
        </div>
        
        <div class="detail-item">
          <span class="detail-label">发生时间:</span>
          <span class="detail-value">{{ errorLog.time }}</span>
        </div>
        
        <div class="error-message">
          <div class="error-title">错误信息:</div>
          <div class="error-content">{{ errorLog.errorMessage }}</div>
        </div>
      </div>
      
      <div v-else class="empty-state">
        <el-empty description="未找到错误日志信息" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useErrorLogStore } from '@/stores/errorLog'
import type { ErrorLog } from '@/types'

const route = useRoute()
const router = useRouter()
const errorLogStore = useErrorLogStore()

const errorLog = ref<ErrorLog | null>(null)

// 获取错误日志详情
const fetchErrorLogDetail = async () => {
  const id = Number(route.params.id)
  if (isNaN(id)) {
    return
  }
  
  const log = errorLogStore.getErrorLogById(id)
  if (log) {
    errorLog.value = log
  }
}

// 返回上一页
const goBack = () => {
  router.go(-1)
}

onMounted(() => {
  fetchErrorLogDetail()
})
</script>

<style scoped lang="scss">
.log-detail-container {
  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    
    .detail-title {
      font-size: 18px;
      font-weight: bold;
    }
  }
  
  .detail-content {
    .detail-item {
      margin-bottom: 15px;
      display: flex;
      
      .detail-label {
        font-weight: bold;
        margin-right: 10px;
        color: #606266;
        min-width: 80px;
      }
      
      .detail-value {
        color: #303133;
        word-break: break-all;
      }
    }
  }
  
  .empty-state {
    padding: 40px 0;
    text-align: center;
  }
}
</style>