# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于Vue.js 3 + Vite + TypeScript构建的错误日志可视化系统，专门用于游戏错误日志的分析和可视化展示。系统具备堆栈符号映射还原功能，可以将混淆后的错误堆栈还原为可读的原始代码位置。

## 常用开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器（端口3000，自动打开浏览器）
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

## 核心架构

### 技术栈
- Vue.js 3 (Composition API)
- Vite 4.x (开发服务器和构建工具)
- TypeScript (类型安全)
- Element Plus (UI组件库)
- ECharts (图表可视化)
- Pinia (状态管理)
- Vue Router (路由管理)
- SCSS (样式预处理)

### 项目结构
- `src/views/` - 页面组件
  - `Dashboard.vue` - 仪表盘页面，包含错误统计图表
  - `Logs.vue` - 错误日志列表页面，支持搜索、过滤、分组
  - `LogDetail.vue` - 错误日志详情页面
- `src/stores/errorLog.ts` - 错误日志状态管理（Pinia store）
- `src/types/index.ts` - TypeScript类型定义
- `src/utils/symbolMapping.ts` - 核心符号映射解析器
- `src/router/index.ts` - 路由配置
- `public/mapping/` - 符号映射文件存储目录

### 状态管理架构

使用Pinia进行状态管理，主要状态存储在`useErrorLogStore`中：

- **数据状态**: `errorLogs`、`loading`、`pagination`
- **过滤搜索**: `searchFilter`支持UID、关键词、版本、时间范围过滤
- **分组功能**: `groupParams`支持按玩家UID、错误类型、版本等分组
- **统计计算**: 自动计算错误类型分布、版本分布、时间趋势等统计数据

### 符号映射还原系统

核心功能位于`src/utils/symbolMapping.ts`：

- **SymbolMappingParser类**: 解析XML格式的符号映射文件
- **多格式支持**: 支持Exception、Normal、Unity等不同堆栈格式
- **映射缓存**: 自动缓存已加载的映射文件提高性能
- **嵌套类处理**: 正确处理Unity中的嵌套类和泛型类型
- **映射文件路径**: `/mapping/symbol-mapping-{platform}-{version}.xml`

### 数据处理流程

1. **文件导入**: 支持JSON格式的错误日志文件上传
2. **数据解析**: 自动识别不同的JSON结构格式
3. **状态更新**: 使用Vue响应式系统更新数据
4. **符号还原**: 按需加载对应平台版本的映射文件进行堆栈还原
5. **可视化展示**: ECharts图表展示统计数据

## 开发注意事项

### 添加新的过滤条件
1. 在`src/types/index.ts`中扩展`SearchFilterParams`接口
2. 在`errorLog.ts`的`filteredErrorLogs`计算属性中添加过滤逻辑
3. 在`Logs.vue`中添加对应的UI控件

### 添加新的分组选项
1. 在`src/types/index.ts`中扩展`GroupByOption`类型
2. 在`errorLog.ts`的`groupedErrorLogs`计算属性中添加分组逻辑
3. 更新`getGroupFieldLabel`方法添加显示名称

### 符号映射文件格式
映射文件应放置在`public/mapping/`目录下，命名格式为`symbol-mapping-{platform}-{version}.xml`，其中platform为`ios`或`apk`。

### 错误日志JSON格式支持
系统支持两种JSON格式：
1. `{ header: [...], data: [...] }` - 带头部信息的格式
2. `[...]` - 直接数组格式

### 开发环境配置
- Vite开发服务器运行在端口3000
- 配置了API代理：`/api` -> `http://localhost:8080`
- 路径别名：`@` -> `./src`
- 支持自动打开浏览器

### TypeScript配置
- 启用严格模式检查
- 配置路径映射支持`@/*`别名
- 包含Vue单文件组件类型支持