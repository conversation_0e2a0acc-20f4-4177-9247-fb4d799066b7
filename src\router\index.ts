import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: {
      title: '仪表盘'
    }
  },
  {
    path: '/logs',
    name: 'Logs',
    component: () => import('@/views/Logs.vue'),
    meta: {
      title: '错误日志'
    }
  },
  {
    path: '/log-detail/:id',
    name: 'LogDetail',
    component: () => import('@/views/LogDetail.vue'),
    meta: {
      title: '日志详情'
    }
  }
]

const router = createRouter({
  history: createWebHistory('/unity-error/'),
  routes
})

// 路由守卫，设置页面标题
router.beforeEach((to, _, next) => {
  document.title = `${to.meta.title} - 错误日志可视化系统`
  next()
})

export default router