import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ErrorLog, ErrorStats, PaginationParams, SearchFilterParams, ChartData, TimeTrendData, DailyErrorData, GroupParams, GroupedData, GroupByOption } from '@/types'
import { symbolMappingParser } from '@/utils/symbolMapping'

// 模拟数据，实际应用中应该从API获取
const mockErrorLogs: ErrorLog[] = [
  {
    id: 1,
    playerUid: 10041975,
    version: '*******',
    ui: 'PageChest,LobbyPanel,ItemTipPanel,ClickEffect,ClickEffect,ModalLayer,ThumbsUpSharePanel,ClickEffect( ->n166_n0mc:btnWelfareCenter->n19_kv7b:btnShare->_n11687:)',
    errorKey: 'NullReferenceException: Object reference not set to an instance of an object.',
    errorMessage: '$Hy.$TWd (System.Object mscorlib, System.Int32 mscorlib, System.Int32 mscorlib) (at :0) $pW.$JyA ($C.$Iw 1) (at :0) $C.$iw.$rsC ($C.$Iw 1) (at :0) $Hy.$MID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$kw.$cSC (System.String 1, $C.$iw 1, System.Object 1, System.Object 1) (at :0) $Hy.$RCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Hy.$UCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$gx.$TJe ($C.$ix 1, $C.$Iw 1) (at :0) $Hy.$yjD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$gx.$qXC ($C.$Iw 1) (at :0) $C.$iw.$rsC ($C.$Iw 1) (at :0) $Hy.$MID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$kw.$CSC (System.String 1, System.Object 1, System.Collections.Generic.List`1[T] 1) (at :0) $Hy.$cCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Hy.$UCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$jqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$GqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$EqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) FairyGUI.StageEngine.LateUpdate () (at :0)',
    ip: '*************',
    time: '2025-07-29 10:08:24',
    device: 'ios'
  },
  {
    id: 2,
    playerUid: 10003814,
    version: '*******',
    ui: 'PageChest,LobbyPanel,PatrolPanel,AchieveReachTips,ModalLayer,PatrolQuickPanel,ClickEffect,ContentPane( ->n21_ms5a:btnReceive->n21_ms5a:btnReceive->n3_4l51:listReward->n20_ms5a:btnPatrolQuick->n4_ms5a:btnCost)',
    errorKey: 'NullReferenceException: Object reference not set to an instance of an object.',
    errorMessage: '$Hy.$ffD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $Pl.$ccb () (at :0) $Hy.$ihD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $Pl.$voD (System.String 1) (at :0) $Hy.$PID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $jf.$ABA ($C.$Iw 1) (at :0) $C.$iw.$rsC ($C.$Iw 1) (at :0) $Hy.$MID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$kw.$CSC (System.String 1, System.Object 1, System.Collections.Generic.List`1[T] 1) (at :0) $Hy.$cCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Hy.$UCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$jqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$GqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$EqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) FairyGUI.StageEngine.LateUpdate () (at :0)',
    ip: '***************',
    time: '2025-07-29 09:49:11',
    device: 'apk'
  },
  {
    id: 3,
    playerUid: 10042726,
    version: '*******',
    ui: 'ItemTipPanel,PageChest,HeroListPanel,LobbyPanel,HandEffect,HeroTrainPanel,ClickEffect,ClickEffect( ->n199_kmjg:btnTabSkin)',
    errorKey: 'ArgumentException: The Object you want to instantiate is null.',
    errorMessage: '$Hy.$KlD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $Ki.$nOA (UnityEngine.GameObject 1) (at :0) $Lc+$Pc.$PJ (System.Object 1) (at :0) $Lc+$rc.$rJ (YooAsset.AssetOperationHandle 1) (at :0) $Hy.$OID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Lc.$iJ (System.String 1, System.Action`1[T] 1) (at :0) $Hy.$DId (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Lc.$GJ (System.String 1, System.String 1, System.Action`1[T] 1) (at :0) $Hy.$eId (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Ki.$lOA () (at :0) $Hy.$ohD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $Ki.$voD (System.String 1) (at :0) $Hy.$PID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $jf.$ABA ($C.$Iw 1) (at :0) $C.$iw.$rsC ($C.$Iw 1) (at :0) $Hy.$MID (System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$kw.$CSC (System.String 1, System.Object 1, System.Collections.Generic.List`1[T] 1) (at :0) $Hy.$cCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $Hy.$UCD (System.Object mscorlib, System.Object mscorlib, System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$jqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$GqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) $C.$TV.$EqC () (at :0) $Hy.$JhD (System.Object mscorlib, System.Int32 mscorlib) (at :0) FairyGUI.StageEngine.LateUpdate () (at :0) UnityEngine.Object.CheckNullArgument (System.Object arg, System.String message) (at :0) UnityEngine.Object.Instantiate[T] (T original) (at :0)',
    ip: '**************',
    time: '2025-07-29 09:36:34',
    device: 'apk'
  },
  {
    id: 4,
    playerUid: 10012345,
    version: '*******',
    ui: 'MainPanel,SettingsPanel',
    errorKey: 'IndexOutOfRangeException: Index was outside the bounds of the array.',
    errorMessage: 'System.IndexOutOfRangeException: Index was outside the bounds of the array.',
    ip: '*************',
    time: '2025-07-29 08:15:22',
    device: 'ios'
  },
  {
    id: 5,
    playerUid: 10067890,
    version: '*******',
    ui: 'BattlePanel,SkillPanel',
    errorKey: 'ArgumentNullException: Value cannot be null.',
    errorMessage: 'System.ArgumentNullException: Value cannot be null.',
    ip: '*********',
    time: '2025-07-29 07:30:15',
    device: 'apk'
  }
]

export const useErrorLogStore = defineStore('errorLog', () => {
  // 状态
  const errorLogs = ref<ErrorLog[]>(mockErrorLogs)
  const loading = ref(false)
  const pagination = ref<PaginationParams>({
    page: 1,
    pageSize: 10,
    total: mockErrorLogs.length
  })
  const searchFilter = ref<SearchFilterParams>({})
  const groupParams = ref<GroupParams>({
    groupBy: 'errorMessage',
    sortOrder: 'desc'
  })
  const expandedGroups = ref<Set<string>>(new Set())
  const expandedLogs = ref<Set<number>>(new Set())
  const restoringLogs = ref<Set<number>>(new Set()) // 正在还原的日志ID

  // 计算属性
  const filteredErrorLogs = computed(() => {
    let result = [...errorLogs.value]
    
    // UID过滤
    if (searchFilter.value.uid) {
      const uid = searchFilter.value.uid.toString()
      result = result.filter(log => 
        log.playerUid.toString().includes(uid)
      )
    }
    
    // 关键字过滤
    if (searchFilter.value.keyword) {
      const keyword = searchFilter.value.keyword.toLowerCase()
      result = result.filter(log => 
        log.errorKey.toLowerCase().includes(keyword) || 
        log.errorMessage.toLowerCase().includes(keyword)
      )
    }
    
    // 版本过滤
    if (searchFilter.value.version) {
      result = result.filter(log => log.version === searchFilter.value.version)
    }

    // 设备过滤
    if (searchFilter.value.device) {
      result = result.filter(log => log.device === searchFilter.value.device)
    }

    // 时间范围过滤
    if (searchFilter.value.startTime && searchFilter.value.endTime) {
      // 将开始时间设置为当天的00:00:00
      const startTime = new Date(searchFilter.value.startTime + ' 00:00:00').getTime()
      // 将结束时间设置为当天的23:59:59
      const endTime = new Date(searchFilter.value.endTime + ' 23:59:59').getTime()
      result = result.filter(log => {
        const logTime = new Date(log.time).getTime()
        return logTime >= startTime && logTime <= endTime
      })
    }
    
    return result
  })

  const paginatedErrorLogs = computed(() => {
    const start = (pagination.value.page - 1) * pagination.value.pageSize
    const end = start + pagination.value.pageSize
    return filteredErrorLogs.value.slice(start, end)
  })

  const errorStats = computed<ErrorStats>(() => {
    const byType: Record<string, number> = {}
    const byVersion: Record<string, number> = {}
    const byTime: Record<string, number> = {}
    const affectedUsers = new Set<number>()
    
    errorLogs.value.forEach(log => {
      // 按错误类型统计
      const errorType = log.errorKey.split(':')[0]
      byType[errorType] = (byType[errorType] || 0) + 1
      
      // 按版本统计
      byVersion[log.version] = (byVersion[log.version] || 0) + 1
      
      // 按时间统计（按小时）
      const hour = log.time.split(' ')[1].split(':')[0]
      const timeKey = `${log.time.split(' ')[0]} ${hour}:00`
      byTime[timeKey] = (byTime[timeKey] || 0) + 1

      // 统计影响的用户数
      affectedUsers.add(log.playerUid)
    })
    
    return {
      total: errorLogs.value.length,
      affectedUsers: affectedUsers.size,
      byType,
      byVersion,
      byTime
    }
  })

  const errorTypeChartData = computed<ChartData[]>(() => {
    const total = errorStats.value.total
    return Object.entries(errorStats.value.byType)
      .map(([name, value]) => {
        const percentage = total > 0 ? Math.round((value / total) * 100) : 0
        return {
          name: `(${percentage}%) ${name}`,
          value
        }
      })
      .sort((a, b) => b.value - a.value) // 按数量从高到低排序
  })

  const versionChartData = computed<ChartData[]>(() => {
    return Object.entries(errorStats.value.byVersion).map(([name, value]) => ({
      name,
      value
    }))
  })

  const timeTrendChartData = computed<TimeTrendData[]>(() => {
    return Object.entries(errorStats.value.byTime)
      .sort(([a], [b]) => new Date(a).getTime() - new Date(b).getTime())
      .map(([time, count]) => ({
        time,
        count
      }))
  })

  // 每日错误数量统计 - 支持按设备类型分组
  const dailyErrorData = computed<DailyErrorData[]>(() => {
    const dailyStats: Record<string, number> = {}
    
    errorLogs.value.forEach(log => {
      const date = log.time.split(' ')[0] // 提取日期部分 YYYY-MM-DD
      dailyStats[date] = (dailyStats[date] || 0) + 1
    })
    
    // 获取最近30天的数据，包括没有错误的日期
    const result: DailyErrorData[] = []
    const today = new Date()
    
    for (let i = 29; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      const dateStr = date.toISOString().split('T')[0] // YYYY-MM-DD格式
      
      result.push({
        date: dateStr,
        count: dailyStats[dateStr] || 0
      })
    }
    
    return result
  })

  // 按设备类型分组的每日错误数量统计
  const dailyErrorDataByDevice = computed(() => {
    const deviceStats: Record<string, Record<string, number>> = {
      'ios': {},
      'apk': {},
      'all': {}
    }
    
    errorLogs.value.forEach(log => {
      const date = log.time.split(' ')[0] // 提取日期部分 YYYY-MM-DD
      const device = log.device || 'unknown'
      
      // 统计各设备类型
      if (device === 'ios' || device === 'apk') {
        deviceStats[device][date] = (deviceStats[device][date] || 0) + 1
      }
      
      // 统计总数
      deviceStats['all'][date] = (deviceStats['all'][date] || 0) + 1
    })
    
    // 获取最近15天的数据
    const today = new Date()
    const result: Record<string, DailyErrorData[]> = {
      'ios': [],
      'apk': [],
      'all': []
    }
    
    for (let i = 14; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(today.getDate() - i)
      const dateStr = date.toISOString().split('T')[0]
      
      result['ios'].push({
        date: dateStr,
        count: deviceStats['ios'][dateStr] || 0
      })
      
      result['apk'].push({
        date: dateStr,
        count: deviceStats['apk'][dateStr] || 0
      })
      
      result['all'].push({
        date: dateStr,
        count: deviceStats['all'][dateStr] || 0
      })
    }
    
    return result
  })

  const versionOptions = computed(() => {
    const versions = [...new Set(errorLogs.value.map(log => log.version))]
    return versions.map(version => ({
      label: version,
      value: version
    }))
  })

  const deviceOptions = computed(() => {
    const devices = [...new Set(errorLogs.value.map(log => log.device).filter(Boolean))]
    return devices.map(device => ({
      label: device === 'ios' ? 'iOS' : device === 'apk' ? 'Android' : device,
      value: device
    }))
  })

  // 分组数据
  const groupedErrorLogs = computed(() => {
    if (groupParams.value.groupBy === 'none') {
      return []
    }

    const groupMap = new Map<string, ErrorLog[]>()
    
    filteredErrorLogs.value.forEach(log => {
      let groupKey = ''
      
      switch (groupParams.value.groupBy) {
        case 'playerUid':
          groupKey = log.playerUid.toString()
          break
        case 'errorKey':
          groupKey = log.errorKey
          break
        case 'version':
          groupKey = log.version
          break
        case 'device':
          groupKey = log.device || '未知设备'
          break
        case 'errorMessage':
          // 取错误信息的前100个字符作为分组键，避免过长
          groupKey = log.errorMessage.length > 100 
            ? log.errorMessage.substring(0, 100) + '...'
            : log.errorMessage
          break
        default:
          groupKey = '未知'
      }
      
      if (!groupMap.has(groupKey)) {
        groupMap.set(groupKey, [])
      }
      groupMap.get(groupKey)!.push(log)
    })

    // 转换为数组并排序
    const grouped: GroupedData[] = Array.from(groupMap.entries()).map(([groupKey, logs]) => ({
      groupKey,
      count: logs.length,
      logs,
      expanded: expandedGroups.value.has(groupKey)
    }))

    // 按数量排序
    grouped.sort((a, b) => {
      return groupParams.value.sortOrder === 'desc' 
        ? b.count - a.count 
        : a.count - b.count
    })

    return grouped
  })

  // 获取分组字段的显示名称
  const getGroupFieldLabel = (groupBy: GroupByOption) => {
    const labels = {
      'none': '无分组',
      'playerUid': '玩家UID',
      'errorKey': '错误KEY',
      'version': '版本号',
      'device': '设备平台',
      'errorMessage': '错误详情'
    }
    return labels[groupBy] || '未知'
  }

  // 方法
  const fetchErrorLogs = async () => {
    loading.value = true
    try {
      // 这里应该是API调用，现在使用模拟数据
      // const response = await api.getErrorLogs()
      // errorLogs.value = response.data
      pagination.value.total = filteredErrorLogs.value.length
    } catch (error) {
      console.error('获取错误日志失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取正确的基础路径
  const getBasePath = () => {
    // 在开发环境中，基础路径是 '/'
    // 在生产环境中，基础路径可能是 '/unity-error/' 等
    const base = (import.meta as any).env?.BASE_URL || '/'
    return base
  }

  // 获取public/data目录下的所有JSON文件
  const getDataDirectoryFiles = async (): Promise<string[]> => {
    try {
      console.log('正在检测可用的数据文件...')
      const basePath = getBasePath()
      console.log(`当前基础路径: ${basePath}`)
      
      // 已知的现有文件列表
      const knownFiles = [
        'export.json'
      ]
      
      const existingFiles: string[] = []
      
      // 检测文件是否存在
      for (const file of knownFiles) {
        // 构建正确的文件路径
        const filePaths = [
          `${basePath}data/${file}`,  // 生产环境路径
          `/data/${file}`             // 开发环境路径
        ]
        
        let found = false
        for (const filePath of filePaths) {
          try {
            console.log(`检测文件路径: ${filePath}`)
            const response = await fetch(filePath, { method: 'HEAD' })
            if (response.ok) {
              console.log(`✓ 找到文件: ${filePath}`)
              existingFiles.push(file)
              found = true
              break
            } else {
              console.log(`✗ 文件不存在: ${filePath} (状态码: ${response.status})`)
            }
          } catch (error) {
            console.log(`✗ 检测文件失败: ${filePath}`, error)
          }
        }
        
        if (!found) {
          console.log(`✗ 所有路径都无法找到文件: ${file}`)
        }
      }
      
      console.log(`共找到 ${existingFiles.length} 个数据文件:`, existingFiles)
      return existingFiles
    } catch (error) {
      console.error('检测数据文件时出错:', error)
      return []
    }
  }

  // 获取最新的JSON文件
  const getLatestDataFile = async (): Promise<string | null> => {
    try {
      const files = await getDataDirectoryFiles()
      
      if (files.length === 0) {
        console.log('未找到任何JSON文件')
        return null
      }

      // 按文件名排序，获取最新的文件（假设文件名包含时间戳）
      const sortedFiles = files.sort((a, b) => {
        // 提取文件名中的时间戳进行比较
        const timeA = a.match(/(\d{4}-\d{2}-\d{2} \d{2}_\d{2}_\d{2})/)
        const timeB = b.match(/(\d{4}-\d{2}-\d{2} \d{2}_\d{2}_\d{2})/)
        
        if (timeA && timeB) {
          const dateA = new Date(timeA[1].replace(/_/g, ':'))
          const dateB = new Date(timeB[1].replace(/_/g, ':'))
          return dateB.getTime() - dateA.getTime()
        }
        
        // 如果无法解析时间戳，按文件名字母序排序
        return b.localeCompare(a)
      })

      console.log('找到的JSON文件:', files)
      console.log('最新文件:', sortedFiles[0])
      return sortedFiles[0]
    } catch (error) {
      console.error('获取最新数据文件失败:', error)
      return null
    }
  }

  // 从最新的JSON文件加载错误日志
  const loadLatestErrorLogs = async () => {
    loading.value = true
    try {
      const latestFile = await getLatestDataFile()
      
      if (!latestFile) {
        console.log('未找到可用的数据文件，使用默认模拟数据')
        // 使用默认的模拟数据
        errorLogs.value.splice(0, errorLogs.value.length, ...mockErrorLogs)
        pagination.value.total = mockErrorLogs.length
        return
      }

      console.log(`正在加载最新数据文件: ${latestFile}`)
      const basePath = getBasePath()
      
      // 尝试多个可能的路径
      const possiblePaths = [
        `${basePath}data/${latestFile}`,  // 生产环境路径
        `/data/${latestFile}`             // 开发环境路径
      ]
      
      let response: Response | null = null
      
      for (const path of possiblePaths) {
        try {
          console.log(`尝试加载路径: ${path}`)
          const res = await fetch(path)
          if (res.ok) {
            response = res
            console.log(`✓ 成功加载: ${path}`)
            break
          }
        } catch (error) {
          console.log(`✗ 加载失败: ${path}`, error)
        }
      }
      
      if (!response) {
        throw new Error(`无法从任何路径加载文件: ${latestFile}`)
      }
      

      const jsonData = await response.json()
      console.log('解析的JSON数据:', jsonData)
      
      // 处理不同的JSON格式
      let logs: ErrorLog[] = []
      
      if (jsonData.data && Array.isArray(jsonData.data)) {
        // 格式: { header: [...], data: [...] }
        console.log('使用格式1: { header: [...], data: [...] }')
        logs = jsonData.data.map((item: any, index: number) => ({
          id: index + 1,
          playerUid: item['玩家UID'],
          version: item['版本号'],
          ui: item['UI'],
          errorKey: item['错误KEY'],
          errorMessage: item['错误信息'],
          ip: item['IP'],
          time: item['时间'],
          device: item['设备']
        }))
      } else if (Array.isArray(jsonData)) {
        // 格式: 直接是数组
        console.log('使用格式2: 直接是数组')
        logs = jsonData.map((item: any, index: number) => ({
          id: index + 1,
          playerUid: item.playerUid || item['玩家UID'],
          version: item.version || item['版本号'],
          ui: item.ui || item['UI'],
          errorKey: item.errorKey || item['错误KEY'],
          errorMessage: item.errorMessage || item['错误信息'],
          ip: item.ip || item['IP'],
          time: item.time || item['时间'],
          device: item.device || item['设备']
        }))
      } else {
        console.log('不支持的JSON格式')
        throw new Error('不支持的JSON格式')
      }
      
      console.log('处理后的日志数据:', logs)
      // 使用Vue的响应式方式更新数组
      errorLogs.value.splice(0, errorLogs.value.length, ...logs)
      pagination.value.total = logs.length
      pagination.value.page = 1
      console.log(`成功加载 ${logs.length} 条错误日志`)
    } catch (error) {
      console.error('加载最新错误日志失败:', error)
      // 如果加载失败，使用默认的模拟数据
      errorLogs.value.splice(0, errorLogs.value.length, ...mockErrorLogs)
      pagination.value.total = mockErrorLogs.length
    } finally {
      loading.value = false
    }
  }

  // 从JSON文件加载错误日志（保留原有的文件上传功能）
  const loadErrorLogsFromFile = (file: File) => {
    return new Promise<void>((resolve, reject) => {
      loading.value = true
      const reader = new FileReader()
      
      reader.onload = (event) => {
        try {
          const jsonData = JSON.parse(event.target?.result as string)
          console.log('解析的JSON数据:', jsonData)
          
          // 处理不同的JSON格式
          let logs: ErrorLog[] = []
          
          if (jsonData.data && Array.isArray(jsonData.data)) {
            // 格式: { header: [...], data: [...] }
            console.log('使用格式1: { header: [...], data: [...] }')
            logs = jsonData.data.map((item: any, index: number) => ({
              id: index + 1,
              playerUid: item['玩家UID'],
              version: item['版本号'],
              ui: item['UI'],
              errorKey: item['错误KEY'],
              errorMessage: item['错误信息'],
              ip: item['IP'],
              time: item['时间'],
              device: item['设备']
            }))
          } else if (Array.isArray(jsonData)) {
            // 格式: 直接是数组
            console.log('使用格式2: 直接是数组')
            logs = jsonData.map((item: any, index: number) => ({
              id: index + 1,
              playerUid: item.playerUid || item['玩家UID'],
              version: item.version || item['版本号'],
              ui: item.ui || item['UI'],
              errorKey: item.errorKey || item['错误KEY'],
              errorMessage: item.errorMessage || item['错误信息'],
              ip: item.ip || item['IP'],
              time: item.time || item['时间'],
              device: item.device || item['设备']
            }))
          } else {
            console.log('不支持的JSON格式')
            throw new Error('不支持的JSON格式')
          }
          
          console.log('处理后的日志数据:', logs)
          // 使用Vue的响应式方式更新数组
          errorLogs.value.splice(0, errorLogs.value.length, ...logs)
          pagination.value.total = logs.length
          pagination.value.page = 1
          console.log('更新后的errorLogs:', errorLogs.value)
          resolve()
        } catch (error) {
          console.error('解析JSON文件失败:', error)
          reject(new Error('JSON文件格式不正确'))
        } finally {
          loading.value = false
        }
      }
      
      reader.onerror = () => {
        loading.value = false
        reject(new Error('读取文件失败'))
      }
      
      reader.readAsText(file)
    })
  }

  const getErrorLogById = (id: number) => {
    return errorLogs.value.find(log => log.id === id)
  }

  const updateSearchFilter = (filter: SearchFilterParams) => {
    searchFilter.value = { ...filter }
    pagination.value.page = 1 // 重置页码
  }

  const updatePagination = (page: number, pageSize?: number) => {
    pagination.value.page = page
    if (pageSize) {
      pagination.value.pageSize = pageSize
    }
  }

  const updateGroupParams = (params: Partial<GroupParams>) => {
    groupParams.value = { ...groupParams.value, ...params }
    // 切换分组类型时清空展开状态
    if (params.groupBy) {
      expandedGroups.value.clear()
    }
  }

  const toggleGroupExpansion = (groupKey: string) => {
    if (expandedGroups.value.has(groupKey)) {
      expandedGroups.value.delete(groupKey)
    } else {
      expandedGroups.value.add(groupKey)
    }
  }

  const toggleLogExpansion = (logId: number) => {
    if (expandedLogs.value.has(logId)) {
      expandedLogs.value.delete(logId)
    } else {
      expandedLogs.value.add(logId)
    }
  }

  const isLogExpanded = (logId: number) => {
    return expandedLogs.value.has(logId)
  }

  // 还原单个日志的堆栈
  const restoreLogStackTrace = async (logId: number) => {
    const log = errorLogs.value.find(l => l.id === logId)
    if (!log || !log.device) {
      console.warn(`日志 ${logId} 不存在或缺少设备信息`)
      return false
    }

    if (log.isRestored) {
      console.log(`日志 ${logId} 已经被还原过了`)
      return true
    }

    restoringLogs.value.add(logId)

    try {
      const restored = await symbolMappingParser.restoreStackTrace(
        log.errorMessage,
        log.device,
        log.version
      )

      // 更新日志对象
      log.restoredErrorMessage = restored
      log.isRestored = true

      console.log(`成功还原日志 ${logId} 的堆栈`)
      return true
    } catch (error) {
      console.error(`还原日志 ${logId} 堆栈失败:`, error)
      return false
    } finally {
      restoringLogs.value.delete(logId)
    }
  }

  // 检查日志是否正在还原
  const isLogRestoring = (logId: number) => {
    return restoringLogs.value.has(logId)
  }

  return {
    // 状态
    errorLogs,
    loading,
    pagination,
    searchFilter,
    groupParams,
    
    // 计算属性
    filteredErrorLogs,
    paginatedErrorLogs,
    errorStats,
    errorTypeChartData,
    versionChartData,
    timeTrendChartData,
    dailyErrorData,
    dailyErrorDataByDevice,
    versionOptions,
    deviceOptions,
    groupedErrorLogs,
    
    // 方法
    fetchErrorLogs,
    loadLatestErrorLogs,
    loadErrorLogsFromFile,
    getErrorLogById,
    updateSearchFilter,
    updatePagination,
    updateGroupParams,
    toggleGroupExpansion,
    toggleLogExpansion,
    isLogExpanded,
    getGroupFieldLabel,
    
    // 堆栈还原方法
    restoreLogStackTrace,
    isLogRestoring
  }
})