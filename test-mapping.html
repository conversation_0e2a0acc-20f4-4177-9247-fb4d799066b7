<!DOCTYPE html>
<html>
<head>
    <title>测试映射还原</title>
</head>
<body>
    <h1>测试映射还原功能</h1>
    <button onclick="testMapping()">测试映射</button>
    <div id="result"></div>

    <script>
        function normalizeStackTraceSignature(signature) {
            // 从 "$Hy.$VhD (System.Object mscorlib, System.Byte mscorlib, System.Int32 mscorlib)"
            // 转换为 "$Hy:$VhD(Object, Byte, Int32)"
            
            // 1. 将 . 替换为 :（仅第一个）
            let normalized = signature.replace(/^([^.]+)\./, '$1:')
            
            // 2. 移除参数中的类型前缀，如 "System.Object mscorlib" -> "Object"
            normalized = normalized.replace(/\(([^)]+)\)/, (match, params) => {
                const cleanParams = params.split(',').map(param => {
                    const trimmed = param.trim()
                    // 移除 System. 前缀和 mscorlib 后缀
                    let cleanParam = trimmed.replace(/^System\./, '')
                    cleanParam = cleanParam.replace(/\s+mscorlib$/, '')
                    cleanParam = cleanParam.replace(/\s+\d+$/, '') // 移除数字后缀如 "1"
                    
                    // 特殊类型映射
                    const typeMap = {
                        'Object': 'Object',
                        'String': 'String', 
                        'Int32': 'Int32',
                        'Single': 'Single',
                        'Boolean': 'Boolean',
                        'Byte': 'Byte'
                    }
                    
                    return typeMap[cleanParam] || cleanParam
                }).join(', ')
                
                return `(${cleanParams})`
            })
            
            // 3. 移除多余的空格
            normalized = normalized.replace(/\s+/g, ' ').trim()
            
            return normalized
        }

        function testMapping() {
            const testSignatures = [
                "$Hy.$VhD (System.Object mscorlib, System.Byte mscorlib, System.Int32 mscorlib)",
                "$C.$zv.$UMC (System.Boolean 1)",
                "$Hy.$UhD (System.Object mscorlib, System.Byte mscorlib, System.Int32 mscorlib)",
                "$C.$ix.$tJe ()",
                "$Hy.$khD (System.Object mscorlib, System.Int32 mscorlib)"
            ]
            
            const expectedMappings = [
                "$Hy:$VhD(Object, Byte, Int32)",
                "$C:$zv$UMC(Boolean)",
                "$Hy:$UhD(Object, Byte, Int32)", 
                "$C:$ix$tJe()",
                "$Hy:$khD(Object, Int32)"
            ]
            
            const resultDiv = document.getElementById('result')
            let html = '<h3>测试结果:</h3>'
            
            testSignatures.forEach((signature, index) => {
                const normalized = normalizeStackTraceSignature(signature)
                const expected = expectedMappings[index]
                const isCorrect = normalized === expected
                
                html += `<div style="margin: 10px 0; padding: 10px; border: 1px solid ${isCorrect ? 'green' : 'red'};">
                    <strong>原始:</strong> ${signature}<br>
                    <strong>标准化:</strong> ${normalized}<br>
                    <strong>期望:</strong> ${expected}<br>
                    <strong>结果:</strong> <span style="color: ${isCorrect ? 'green' : 'red'}">${isCorrect ? '正确' : '错误'}</span>
                </div>`
            })
            
            resultDiv.innerHTML = html
        }
    </script>
</body>
</html>