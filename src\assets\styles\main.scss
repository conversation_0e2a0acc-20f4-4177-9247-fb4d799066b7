// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f0f2f5;
}

#app {
  height: 100%;
}

// 卡片样式
.card {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

// 卡片标题
.card-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

// 统计卡片样式
.stat-card {
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
  }
  
  .stat-label {
    color: #909399;
    font-size: 14px;
  }
}

// 图表容器样式
.chart-container {
  height: 300px;
  width: 100%;
}

// 表格样式
.table-container {
  .table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }
  
  .table-title {
    font-size: 16px;
    font-weight: bold;
  }
  
  .table-actions {
    display: flex;
    gap: 10px;
  }
}

// 分页样式
.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

// 搜索表单样式
.search-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

// 详情页样式
.detail-container {
  .detail-item {
    margin-bottom: 15px;
    
    .detail-label {
      font-weight: bold;
      margin-right: 10px;
      color: #606266;
    }
    
    .detail-value {
      color: #303133;
    }
  }
  
  .error-message {
    background-color: #fef0f0;
    border-left: 4px solid #f56c6c;
    padding: 15px;
    margin-top: 20px;
    border-radius: 4px;
    
    .error-title {
      font-weight: bold;
      color: #f56c6c;
      margin-bottom: 10px;
    }
    
    .error-content {
      font-family: monospace;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

// 响应式布局
@media (max-width: 768px) {
  .el-aside {
    width: 64px !important;
    
    .el-menu-item span {
      display: none;
    }
  }
  
  .stat-card {
    margin-bottom: 15px;
  }
  
  .chart-container {
    height: 250px;
  }
}