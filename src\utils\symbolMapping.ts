// 符号映射工具类，用于解析映射文件并还原混淆的堆栈跟踪

// 用于表示单个方法重载的映射
export interface MethodSignatureMapping {
  newMethodParameters: string;
  oldMethodNameWithDeclaringType: string;
  oldMethodParameters: string;
}

// 用于表示一个方法（可能包含多个重载）
export interface MethodSignature {
  newMethodNameWithDeclaringType: string;
  mappings: MethodSignatureMapping[];
}

export interface TypeMapping {
  originalName: string
  obfuscatedName: string
}

export interface SymbolMapping {
  platform: string
  version: string
  methods: Map<string, MethodSignature>
  types: Map<string, TypeMapping>
}

export class SymbolMappingParser {
  private mappingCache = new Map<string, SymbolMapping>()
  
  // 基于focus-creative-games/obfuz-tools的正则表达式模式
  // 匹配: at Namespace.Class`1[T].Method[K](Type a, Type b) in /path/to/file:line
  private readonly exceptionStackTraceRegex = /^(\s*at\s+)([^[\]\s]*[^.])((\[[^[\]\s]+\])?)\.(\S[^[\]\s]*)((\[[^[\]\s]+\])?)\s+(\(.*\))(\s+\[\S+\]\s+in)/;
  // 匹配: Namespace.Class:Method(Type,Type)
  private readonly normalStackTraceRegex = /^(\S+):(\S+)(\([^)]*\))$/;
  // 匹配 Unity 格式: Class.Method (params) (at :line)
  private readonly unityStackTraceRegex = /^(.+?)\.([^.\s]+)\s+(\([^)]*)\)\s+(\(at\s*:?\d*\))$/;
  // 匹配包含嵌套类的 Unity 格式: OuterClass+InnerClass.Method (params) (at :line)
  // 支持泛型格式如: Panel+$mf`1[T].$TBA
  private readonly unityNestedClassRegex = /^(.+?\+[^.\s]+(?:`\d+\[[^\]]+\])?)\.([^.\s]+)\s+(\([^)]*)\)\s+(\(at\s*:?\d*\))$/;
  // 匹配混淆后的类型名称，例如 $Abc.Def
  private readonly typeNameRegex = /\$[$a-zA-Z_]+([./]\$[$a-zA-Z_]+)*/g

  // 生成映射文件路径
  private getMappingFilePath(platform: string, version: string): string {
    // 在生产环境中考虑Vite的base路径
    const basePath = (import.meta as any).env?.BASE_URL || '/'
    const mappingPath = `mapping/symbol-mapping-${platform}-${version}.xml`
    return basePath.endsWith('/') ? `${basePath}${mappingPath}` : `${basePath}/${mappingPath}`
  }

  // 获取映射缓存键
  private getCacheKey(platform: string, version: string): string {
    return `${platform}-${version}`
  }

  // 分割方法签名，例如 "ClassName:MethodName(params)" -> ["ClassName:MethodName", "(params)"]
  private splitMethodSignature(signature: string): [string, string] {
    const index = signature.indexOf('(');
    if (index < 0) {
      return [signature, ''];
    }
    const methodNameWithDeclaringType = signature.substring(0, index);
    const methodParameters = signature.substring(index);
    return [methodNameWithDeclaringType, methodParameters];
  }

  // 解析XML映射文件
  private parseXMLMapping(xmlContent: string, platform: string, version: string): SymbolMapping {
    const methods = new Map<string, MethodSignature>()
    const types = new Map<string, TypeMapping>()

    const parser = new DOMParser()
    const xmlDoc = parser.parseFromString(xmlContent, 'application/xml')

    const methodNodes = xmlDoc.getElementsByTagName('method')

    for (let i = 0; i < methodNodes.length; i++) {
      const methodNode = methodNodes[i]
      const oldStackTraceSignature = methodNode.getAttribute('oldStackTraceSignature') || ''
      const newStackTraceSignature = methodNode.getAttribute('newStackTraceSignature') || ''

      if (oldStackTraceSignature && newStackTraceSignature) {
        const [oldMethodNameWithDeclaringType, oldMethodParameters] = this.splitMethodSignature(oldStackTraceSignature);
        const [newMethodNameWithDeclaringType, newMethodParameters] = this.splitMethodSignature(newStackTraceSignature);

        let methodSignature = methods.get(newMethodNameWithDeclaringType);
        if (!methodSignature) {
          methodSignature = {
            newMethodNameWithDeclaringType: newMethodNameWithDeclaringType,
            mappings: [],
          };
          methods.set(newMethodNameWithDeclaringType, methodSignature);
        }

        methodSignature.mappings.push({
          newMethodParameters,
          oldMethodNameWithDeclaringType,
          oldMethodParameters,
        });
      }
    }

    const typeNodes = xmlDoc.getElementsByTagName('type')
    for (let i = 0; i < typeNodes.length; i++) {
      const typeNode = typeNodes[i]
      const fullName = typeNode.getAttribute('fullName') || ''
      const newFullName = typeNode.getAttribute('newFullName') || ''
      // const deobfuscatedName = typeNode.getAttribute('signature') || fullName

      if (fullName && newFullName) {
        const typeMap = {
          originalName: fullName, // 使用 fullName 作为原始名称
          obfuscatedName: newFullName
        }
        types.set(newFullName, typeMap)
        if (fullName !== newFullName) {
          types.set(fullName, typeMap)
        }
      }
    }

    return {
      platform,
      version,
      methods,
      types
    }
  }

  // 加载并解析映射文件
  async loadMapping(platform: string, version: string): Promise<SymbolMapping | null> {
    const cacheKey = this.getCacheKey(platform, version)
    
    // 检查缓存
    if (this.mappingCache.has(cacheKey)) {
      return this.mappingCache.get(cacheKey)!
    }

    try {
      const filePath = this.getMappingFilePath(platform, version)
      const response = await fetch(filePath)
      
      if (!response.ok) {
        console.warn(`映射文件不存在: ${filePath}`)
        return null
      }
      
      const xmlContent = await response.text()
      const mapping = this.parseXMLMapping(xmlContent, platform, version)
      
      // 缓存结果
      this.mappingCache.set(cacheKey, mapping)
      
      console.log(`成功加载映射文件: ${filePath}, 共 ${mapping.methods.size} 个方法映射`)
      
      // 输出前几个映射示例用于调试
      let count = 0;
      for (const [, value] of mapping.methods) {
        if (count < 3) {
          const firstMapping = value.mappings[0];
          if (firstMapping) {
            const newSig = `${value.newMethodNameWithDeclaringType}${firstMapping.newMethodParameters}`;
            const oldSig = `${firstMapping.oldMethodNameWithDeclaringType}${firstMapping.oldMethodParameters}`;
            console.log(`映射示例 ${count + 1}: "${newSig}" -> "${oldSig}"`);
          }
          count++;
        } else {
          break;
        }
      }
      
      return mapping
    } catch (error) {
      console.error(`加载映射文件失败 ${platform}-${version}:`, error)
      return null
    }
  }

  // 辅助函数，用于分割 C# 风格的类型和方法名
  private splitMethodNameWithDeclaringTypeName(name: string): [string, string] {
    const lastColonIndex = name.lastIndexOf(':');
    if (lastColonIndex !== -1) {
        const declaringTypeName = name.substring(0, lastColonIndex);
        const methodName = name.substring(lastColonIndex + 1);
        return [declaringTypeName, methodName];
    }
    const lastDotIndex = name.lastIndexOf('.');
     if (lastDotIndex !== -1) {
        const declaringTypeName = name.substring(0, lastDotIndex);
        const methodName = name.substring(lastDotIndex + 1);
        return [declaringTypeName, methodName];
    }
    return ['', name]; // 如果没有找到分隔符，则返回空声明类型
  }

  // 基于focus-creative-games/obfuz-tools的堆栈跟踪还原逻辑
  private tryDeobfuscateStackTraceLine(line: string, mapping: SymbolMapping): string {
    let deobfuscatedLine = line;
    let match: RegExpMatchArray | null;

    // 1. 尝试匹配 Exception 格式的堆栈
    if ((match = line.match(this.exceptionStackTraceRegex))) {
      const obfuscatedDeclaringTypeName = match[2];
      const obfuscatedMethodName = match[5];
      const obfuscatedExceptionMethodNameWithDeclaringType = `${obfuscatedDeclaringTypeName}:${obfuscatedMethodName}`;
      const obfuscatedMethodParameters = match[8];
      
      const methodSignature = mapping.methods.get(obfuscatedExceptionMethodNameWithDeclaringType);
      if (methodSignature) {
        let foundMapping: MethodSignatureMapping | undefined = methodSignature.mappings.find(m => m.newMethodParameters === obfuscatedMethodParameters);
        
        if (!foundMapping && methodSignature.mappings.length > 0) {
          foundMapping = methodSignature.mappings[0];
        }

        if (foundMapping) {
          const [oldDeclaringTypeName, oldMethodName] = this.splitMethodNameWithDeclaringTypeName(foundMapping.oldMethodNameWithDeclaringType);
          deobfuscatedLine = `${match[1]}${oldDeclaringTypeName}${match[3]}.${oldMethodName}${match[6]}${foundMapping.oldMethodParameters}${match[9]}`;
        }
      }
    }
    // 2. 尝试匹配 Normal 格式的堆栈
    else if ((match = line.match(this.normalStackTraceRegex))) {
      const obfuscatedDeclaringTypeName = match[1];
      const obfuscatedMethodName = match[2];
      const obfuscatedMethodNameWithDeclaringType = `${obfuscatedDeclaringTypeName}:${obfuscatedMethodName}`;
      const obfuscatedMethodParameters = match[3];

      const methodSignature = mapping.methods.get(obfuscatedMethodNameWithDeclaringType);
      if (methodSignature) {
        let foundMapping = methodSignature.mappings.find(m => m.newMethodParameters === obfuscatedMethodParameters);

        if (!foundMapping && methodSignature.mappings.length > 0) {
          foundMapping = methodSignature.mappings[0];
        }
        
        if (foundMapping) {
          deobfuscatedLine = `${foundMapping.oldMethodNameWithDeclaringType}${foundMapping.oldMethodParameters}`;
        }
      }
    }
    // 3. 尝试匹配包含嵌套类的 Unity 格式
    else if ((match = line.match(this.unityNestedClassRegex))) {
      const fullObfuscatedTypeName = match[1]; // 例如 "PlatformBase+$bA"
      const obfuscatedMethodName = match[2];   // 例如 "$BU"
      const obfuscatedMethodParameters = match[3]; // 例如 "()"
      
      // 分离外部类和内部类，处理泛型参数
      const plusIndex = fullObfuscatedTypeName.lastIndexOf('+');
      if (plusIndex !== -1) {
        const outerClass = fullObfuscatedTypeName.substring(0, plusIndex);
        let innerClass = fullObfuscatedTypeName.substring(plusIndex + 1);
        
        // 处理泛型参数：Panel+$mf`1[T] -> innerClass = $mf`1[T]
        // 但在映射中，键通常是 $mf`1:$TBA 的格式
        let innerClassForMapping = innerClass;
        
        // 如果包含泛型参数，提取基础类名
        const genericMatch = innerClass.match(/^([^`]+)(`\d+)(\[.+\])?$/);
        if (genericMatch) {
          // $mf`1[T] -> $mf`1 (保留反引号部分，去掉方括号部分)
          innerClassForMapping = genericMatch[1] + genericMatch[2];
        }
        
        // 尝试多种可能的方法映射键格式
        const possibleMethodNames = [
          `${innerClassForMapping}:${obfuscatedMethodName}`, // 例如: $mf`1:$TBA
          `${innerClass}:${obfuscatedMethodName}`,           // 例如: $mf`1[T]:$TBA （包含方括号）
          `<>c:${obfuscatedMethodName}`,                     // 例如: <>c:$LW （对于未重命名的 <>c 类）
          `${fullObfuscatedTypeName}:${obfuscatedMethodName}` // 完整格式
        ];
        
        let foundMapping: MethodSignatureMapping | undefined;
        let methodSignature: MethodSignature | undefined;
        
        for (const methodNameWithType of possibleMethodNames) {
          methodSignature = mapping.methods.get(methodNameWithType);
          if (methodSignature) {
            foundMapping = methodSignature.mappings.find(m => m.newMethodParameters === obfuscatedMethodParameters);
            if (!foundMapping && methodSignature.mappings.length > 0) {
              foundMapping = methodSignature.mappings[0];
            }
            if (foundMapping) break;
          }
        }
        
        if (foundMapping) {
          const [oldDeclaringTypeName, oldMethodName] = this.splitMethodNameWithDeclaringTypeName(foundMapping.oldMethodNameWithDeclaringType);
          
          // 重构完整的原始类名
          let fullOriginalTypeName = oldDeclaringTypeName;
          
          // 特殊处理未重命名的嵌套类（如 <>c）
          if (oldDeclaringTypeName === '<>c') {
            fullOriginalTypeName = `${outerClass}.<>c`;
          }
          // 处理 DisplayClass 格式的嵌套类
          else if (oldDeclaringTypeName.includes('<>c__DisplayClass')) {
            // 从type映射中查找完整的原始类名，尝试多种格式
            let typeMapping = mapping.types.get(fullObfuscatedTypeName);
            
            // 如果没找到且是泛型，尝试查找基础类型映射
            if (!typeMapping && genericMatch) {
              const baseTypeName = `${outerClass}+${innerClassForMapping}`;
              typeMapping = mapping.types.get(baseTypeName);
            }
            
            if (typeMapping && typeMapping.originalName.includes('DisplayClass')) {
              // 正确解码 HTML 实体并转换路径分隔符
              let originalName = typeMapping.originalName
                .replace('&lt;', '<')
                .replace('&gt;', '>')
                .replace('/&lt;&gt;c__DisplayClass', '.<>c__DisplayClass')
                .replace('/', '.');
              
              // 如果原始堆栈包含泛型参数，需要保留
              if (genericMatch) {
                // 将 `1 转换为正确的泛型格式 `1
                originalName = originalName.replace(/`(\d+)$/, '`$1');
              }
              
              fullOriginalTypeName = originalName;
            } else {
              // 回退：直接使用原始 DisplayClass 名称
              fullOriginalTypeName = `${outerClass}.${oldDeclaringTypeName}`;
            }
          }
          
          deobfuscatedLine = `${fullOriginalTypeName}.${oldMethodName}${foundMapping.oldMethodParameters} ${match[4]}`;
        }
      }
    }
    // 4. 尝试匹配普通 Unity 格式的堆栈
    else if ((match = line.match(this.unityStackTraceRegex))) {
      const obfuscatedDeclaringTypeName = match[1];
      const obfuscatedMethodName = match[2];
      const obfuscatedMethodNameWithDeclaringType = `${obfuscatedDeclaringTypeName}:${obfuscatedMethodName}`;
      const obfuscatedMethodParameters = match[3];

      const methodSignature = mapping.methods.get(obfuscatedMethodNameWithDeclaringType);
      if (methodSignature) {
          let foundMapping = methodSignature.mappings.find(m => m.newMethodParameters === obfuscatedMethodParameters);

          if (!foundMapping && methodSignature.mappings.length > 0) {
              foundMapping = methodSignature.mappings[0];
          }
          
          if (foundMapping) {
              const [oldDeclaringTypeName, oldMethodName] = this.splitMethodNameWithDeclaringTypeName(foundMapping.oldMethodNameWithDeclaringType);
              deobfuscatedLine = `${oldDeclaringTypeName}.${oldMethodName}${foundMapping.oldMethodParameters} ${match[4]}`;
          }
      }
    }
    
    // 5. 无论如何，都对整行进行类型名称的反混淆
    return this.deobfuscateTypeNames(deobfuscatedLine, mapping);
  }

  // 反混淆类型名称
  private deobfuscateTypeNames(text: string, mapping: SymbolMapping): string {
    return text.replace(this.typeNameRegex, (match) => {
      const typeMapping = mapping.types.get(match)
      if (typeMapping) {
        console.log(`找到类型映射: "${match}" -> "${typeMapping.originalName}"`)
        return typeMapping.originalName
      }
      return match
    })
  }

  // 还原完整的错误堆栈
  async restoreStackTrace(errorMessage: string, platform: string, version: string): Promise<string> {
    const mapping = await this.loadMapping(platform, version)
    
    if (!mapping) {
      console.warn(`无法加载映射文件 ${platform}-${version}，返回原始堆栈`)
      return errorMessage
    }

    console.log(`开始还原堆栈，平台: ${platform}, 版本: ${version}`)
    console.log(`原始错误信息:`, errorMessage)
    
    // 优先使用正则表达式匹配所有堆栈帧，以处理单行、空格分隔的堆栈
    const frameRegex = /([^\n]+?\(at\s*:[^)]+\))/g;
    let lines: string[] = errorMessage.match(frameRegex) || [];

    // 如果没有匹配到任何帧 (例如，非堆栈错误信息或格式不同), 则按行分割
    if (lines.length === 0) {
      lines = errorMessage.split('\n');
    }

    const restoredLines: string[] = []
    
    for (const line of lines) {
      let processedLine = line.trim()
      
      if (processedLine) {
        // 尝试分离连续的方法调用 (如: Method1() Method2() Method3())
        const separatedMethods = this.separateMethodCalls(processedLine)
        if (separatedMethods.length > 1) {
          // 如果是连续方法调用，分别处理每个方法
          const restoredMethods = separatedMethods.map(method => 
            this.tryDeobfuscateStackTraceLine(method.trim(), mapping)
          ).filter(method => method.trim() !== '')
          restoredLines.push(restoredMethods.join('\n'))
        } else {
          // 普通单个堆栈帧
          const restoredLine = this.tryDeobfuscateStackTraceLine(processedLine, mapping)
          restoredLines.push(restoredLine)
        }
      }
    }

    const restoredMessage = restoredLines.join('\n')
    console.log(`还原后错误信息:`, restoredMessage)
    return restoredMessage
  }

  // 分离连续的方法调用
  private separateMethodCalls(line: string): string[] {
    // 匹配 Class:Method(params) 格式，支持混淆类名如 $Obfuz$ProxyCall
    const methodPattern = /([^:\s]+:[^:\s(]+\([^)]*\))/g
    const matches = line.match(methodPattern)
    
    if (matches && matches.length > 1) {
      return matches
    }
    
    return [line]
  }


  // 清空缓存
  clearCache(): void {
    this.mappingCache.clear()
  }

  // 获取缓存状态
  getCacheStatus(): Array<{platform: string; version: string; methodCount: number}> {
    const status: Array<{platform: string; version: string; methodCount: number}> = []
    
    this.mappingCache.forEach((mapping) => {
      status.push({
        platform: mapping.platform,
        version: mapping.version,
        methodCount: mapping.methods.size
      })
    })
    
    return status
  }
}

// 导出单例实例
export const symbolMappingParser = new SymbolMappingParser()