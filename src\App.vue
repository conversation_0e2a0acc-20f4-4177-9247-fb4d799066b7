<template>
  <div class="app-container">
    <el-container>
      <el-header class="app-header">
        <div class="logo">错误日志可视化系统</div>
        <div class="header-right">
          <el-upload
            class="upload-demo"
            :show-file-list="false"
            accept=".json"
            :http-request="customUpload"
          >
            <el-button type="primary" size="small">
              <el-icon><Upload /></el-icon>
              上传JSON文件
            </el-button>
          </el-upload>
          <el-dropdown>
            <span class="user-info">
              <el-icon><User /></el-icon>
              管理员
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>个人信息</el-dropdown-item>
                <el-dropdown-item>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>
      <el-container>
        <el-aside width="200px" class="app-aside">
          <el-menu
            :default-active="activeMenu"
            router
            class="app-menu"
          >
            <el-menu-item index="/">
              <el-icon><DataLine /></el-icon>
              <span>仪表盘</span>
            </el-menu-item>
            <el-menu-item index="/logs">
              <el-icon><Document /></el-icon>
              <span>错误日志</span>
            </el-menu-item>
          </el-menu>
        </el-aside>
        <el-main class="app-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useErrorLogStore } from '@/stores/errorLog'
import { ElMessage } from 'element-plus'

const route = useRoute()
const activeMenu = computed(() => route.path)
const errorLogStore = useErrorLogStore()

// 应用启动时自动加载最新数据
onMounted(async () => {
  try {
    console.log('应用启动，正在加载最新错误日志数据...')
    await errorLogStore.loadLatestErrorLogs()
  } catch (error) {
    console.error('自动加载数据失败:', error)
    // 不显示错误消息，因为会回退到模拟数据
  }
})

// 自定义文件上传处理
const customUpload = async ({ file }: { file: File }) => {
  try {
    await errorLogStore.loadErrorLogsFromFile(file)
    ElMessage.success('文件上传成功')
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '文件上传失败')
    console.error('文件上传失败:', error)
  }
}
</script>

<style scoped lang="scss">
.app-container {
  height: 100vh;
  width: 100vw;
  
  .app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #409eff;
    color: white;
    padding: 0 20px;
    
    .logo {
      font-size: 20px;
      font-weight: bold;
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 15px;
      
      .user-info {
        display: flex;
        align-items: center;
        cursor: pointer;
        
        .el-icon {
          margin-right: 5px;
        }
      }
    }
  }
  
  .app-aside {
    background-color: #f5f7fa;
    
    .app-menu {
      border-right: none;
    }
  }
  
  .app-main {
    background-color: #f0f2f5;
    padding: 20px;
  }
}
</style>