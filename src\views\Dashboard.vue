<template>
  <div class="dashboard-container">
    <el-row :gutter="20">
      <!-- 错误总数统计卡片 -->
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <div class="card stat-card">
          <div class="stat-label">错误总数</div>
          <div class="stat-value">{{ errorStats.total }}</div>
          <el-icon size="24" color="#F56C6C"><Warning /></el-icon>
        </div>
      </el-col>

      <!-- 影响人数统计卡片 -->
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <div class="card stat-card">
          <div class="stat-label">影响人数</div>
          <div class="stat-value">{{ errorStats.affectedUsers }}</div>
          <el-icon size="24" color="#9f525bff"><User /></el-icon>
        </div>
      </el-col>
      
      <!-- 错误类型统计卡片 -->
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <div class="card stat-card">
          <div class="stat-label">错误类型</div>
          <div class="stat-value">{{ Object.keys(errorStats.byType).length }}</div>
          <el-icon size="24" color="#E6A23C"><Files /></el-icon>
        </div>
      </el-col>
      
      <!-- 版本统计卡片 -->
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <div class="card stat-card">
          <div class="stat-label">涉及版本</div>
          <div class="stat-value">{{ Object.keys(errorStats.byVersion).length }}</div>
          <el-icon size="24" color="#409EFF"><Collection /></el-icon>
        </div>
      </el-col>
      
      <!-- 今日错误统计卡片 -->
      <el-col :xs="24" :sm="12" :md="4" :lg="4" :xl="4">
        <div class="card stat-card">
          <div class="stat-label">今日错误</div>
          <div class="stat-value">{{ todayErrorCount }}</div>
          <el-icon size="24" color="#67C23A"><Calendar /></el-icon>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <!-- 错误类型分布饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="card">
          <div class="card-title">错误类型分布</div>
          <div class="chart-container">
            <v-chart :option="errorTypeChartOption" autoresize />
          </div>
        </div>
      </el-col>
      
      <!-- 版本分布统计 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="12" :xl="12">
        <div class="card">
          <div class="card-title">版本分布统计</div>
          <div class="chart-container">
            <v-chart :option="versionChartOption" autoresize />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <!-- 每日错误数量曲线图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <div class="card">
          <div class="card-title">每日错误数量趋势（最近15天）</div>
          <div class="chart-container">
            <v-chart :option="dailyErrorChartOption" autoresize />
          </div>
        </div>
      </el-col>
    </el-row>
    
    <el-row :gutter="20">
      <!-- 错误时间趋势折线图 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
        <div class="card">
          <div class="card-title">错误时间趋势</div>
          <div class="chart-container">
            <v-chart :option="timeTrendChartOption" autoresize />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { PieChart, LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import VChart from 'vue-echarts'
import { useErrorLogStore } from '@/stores/errorLog'
import dayjs from 'dayjs'

// 注册ECharts组件
use([
  CanvasRenderer,
  PieChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const errorLogStore = useErrorLogStore()

// 获取错误统计信息
const errorStats = computed(() => errorLogStore.errorStats)

// 计算今日错误数量
const todayErrorCount = computed(() => {
  const today = dayjs().format('YYYY-MM-DD')
  return errorLogStore.errorLogs.filter(log =>
    log.time.startsWith(today)
  ).length
})

// 错误类型分布图表配置
const errorTypeChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 10,
    data: errorLogStore.errorTypeChartData.map(item => item.name)
  },
  series: [
    {
      name: '错误类型',
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '18',
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: errorLogStore.errorTypeChartData
    }
  ]
}))

// 版本分布图表配置
const versionChartOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 10,
    data: errorLogStore.versionChartData.map(item => item.name)
  },
  series: [
    {
      name: '版本分布',
      type: 'pie',
      radius: '50%',
      data: errorLogStore.versionChartData,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}))

// 每日错误数量图表配置
const dailyErrorChartOption = computed(() => {
  const deviceData = errorLogStore.dailyErrorDataByDevice
  
  // 获取X轴数据（日期）
  const xAxisData = (deviceData.all || []).map(item => {
    const date = new Date(item.date)
    return `${date.getMonth() + 1}-${date.getDate()}`
  })
  
  return {
    tooltip: {
      trigger: 'axis',
      formatter: function(params: any) {
        let result = `日期: ${params[0].axisValue}<br/>`
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: ['iOS错误数量', 'APK错误数量'],
      bottom: 0
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        rotate: 45,
        interval: 1 // 每隔1个标签显示一个，避免15天标签过于拥挤
      }
    },
    yAxis: {
      type: 'value',
      name: '错误数量',
      minInterval: 1
    },
    series: [
      {
        name: 'iOS错误数量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#007AFF',
          width: 3
        },
        itemStyle: {
          color: '#007AFF'
        },
        data: (deviceData.ios || []).map(item => item.count)
      },
      {
        name: 'APK错误数量',
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#34C759',
          width: 3
        },
        itemStyle: {
          color: '#34C759'
        },
        data: (deviceData.apk || []).map(item => item.count)
      }
    ]
  }
})

// 时间趋势图表配置
const timeTrendChartOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: errorLogStore.timeTrendChartData.map(item => item.time)
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '错误数量',
      type: 'line',
      stack: 'Total',
      smooth: true,
      areaStyle: {
        color: 'rgba(64, 158, 255, 0.2)'
      },
      lineStyle: {
        color: '#409EFF',
        width: 2
      },
      data: errorLogStore.timeTrendChartData.map(item => item.count)
    }
  ]
}))

onMounted(() => {
  errorLogStore.fetchErrorLogs()
})
</script>

<style scoped lang="scss">
.dashboard-container {
  .el-row {
    margin-bottom: 20px;
  }
  
  .stat-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: 120px;
    
    .el-icon {
      margin-top: 10px;
    }
  }
  
}
</style>