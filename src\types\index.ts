// 错误日志数据类型定义
export interface ErrorLog {
  id: number
  playerUid: number
  version: string
  ui: string
  errorKey: string
  errorMessage: string
  ip: string
  time: string
  device?: string  // 设备平台（ios/apk）
  restoredErrorMessage?: string  // 还原后的错误信息
  isRestored?: boolean  // 是否已还原
}

// 错误统计信息
export interface ErrorStats {
  total: number
  affectedUsers: number
  byType: Record<string, number>
  byVersion: Record<string, number>
  byTime: Record<string, number>
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total: number
}

// 搜索过滤参数
export interface SearchFilterParams {
  uid?: string
  keyword?: string
  version?: string
  device?: string
  startTime?: string
  endTime?: string
}

// 分组选项
export type GroupByOption = 'none' | 'playerUid' | 'errorKey' | 'version' | 'device' | 'errorMessage'

// 分组数据
export interface GroupedData {
  groupKey: string
  count: number
  logs: ErrorLog[]
  expanded?: boolean
}

// 分组参数
export interface GroupParams {
  groupBy: GroupByOption
  sortOrder: 'asc' | 'desc'
}

// 图表数据
export interface ChartData {
  name: string
  value: number
}

// 时间趋势数据
export interface TimeTrendData {
  time: string
  count: number
}

// 每日错误统计数据
export interface DailyErrorData {
  date: string
  count: number
}